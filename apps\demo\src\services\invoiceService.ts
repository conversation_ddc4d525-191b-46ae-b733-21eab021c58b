import axios from 'axios';
import { InvoiceResponse } from '@/types/subscription';

// Create an axios instance for invoice API
const invoiceApi = axios.create({
  baseURL: 'http://localhost:1005',
  headers: {
    'Content-Type': 'application/json',
  },
});

export const getInvoicesByEmail = async (email: string): Promise<InvoiceResponse[]> => {
  try {
    const response = await invoiceApi.get<InvoiceResponse[]>(`/invoices/by-email/${email}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching invoices:', error);
    throw error;
  }
};

export const downloadInvoiceById = async (invoiceId: string): Promise<void> => {
  try {
    const response = await invoiceApi.get(`/invoices/download/${invoiceId}`, {
      responseType: 'blob',
    });

    // Create blob link to download
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    
    // Extract filename from response headers or use default
    const contentDisposition = response.headers['content-disposition'];
    let filename = 'invoice.pdf';
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/);
      if (filenameMatch) {
        filename = filenameMatch[1];
      }
    }
    
    link.setAttribute('download', filename);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error downloading invoice:', error);
    throw error;
  }
};

export const downloadInvoiceByNumber = async (invoiceNumber: string): Promise<void> => {
  try {
    const response = await invoiceApi.get(`/invoices/download/by-number/${invoiceNumber}`, {
      responseType: 'blob',
    });

    // Create blob link to download
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    
    // Extract filename from response headers or use default
    const contentDisposition = response.headers['content-disposition'];
    let filename = 'invoice.pdf';
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/);
      if (filenameMatch) {
        filename = filenameMatch[1];
      }
    }
    
    link.setAttribute('download', filename);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error downloading invoice:', error);
    throw error;
  }
};
