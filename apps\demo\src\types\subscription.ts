export enum BillingInterval {
  TWOMINUTE = 'TWOMINUTE',
  HALFHOUR = 'HALFHOUR',
  DAILY = 'DAILY',
}

export interface UserData {
  externalId: string;
  email: string;
  username: string;
  authProvider: string;
}

export interface SubscriptionPlan {
  externalId: string;
  systemSubscriptionPlan: string;
  endDate: string;
  price: number;
  billingInterval: BillingInterval;
}

export interface RedirectUrls {
  successUrl: string;
  failureUrl: string;
  baseUrl: string;
}

export interface SubscriptionRequest {
  user_data: UserData;
  subscription_plan: SubscriptionPlan;
  redirect_urls: RedirectUrls;
}

export interface SubscriptionResponse {
  statuscode: number;
  amount: string;
  itemd: string;
  merchantid: string;
  idDemande: string;
  url: string;
  status: string | null;
}

export interface TransactionResponse {
  id: number;
  reference: string;
  transactionReference: string;
  amount: number;
  currency: string;
  status: string;
  paymentProcessor: string;
  cardLastFour: string;
  processedAt: string;
  createdAt: string;
  externalTransactionId: string;
}

export interface InvoiceResponse {
  id: string;
  invoiceNumber: string;
  createdAt: string;
  status: string;
  amount: number;
  currency: string;
  customerName: string;
  customerEmail: string;
}
