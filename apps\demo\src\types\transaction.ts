export interface TransactionResponse {
  id: number;
  reference: string;
  transactionReference: string;
  amount: number;
  currency: string;
  status: string;
  paymentProcessor: string;
  cardLastFour: string;
  processedAt: string;
  createdAt: string;
  externalTransactionId: string;
}

export interface TransactionListResponse {
  transactions: TransactionResponse[];
  total: number;
  page: number;
  limit: number;
}

export enum TransactionStatus {
  PENDING = 'PENDING',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  REFUNDED = 'REFUNDED'
}

export interface TransactionFilter {
  status?: TransactionStatus;
  startDate?: string;
  endDate?: string;
  minAmount?: number;
  maxAmount?: number;
}
