import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Layout from '@/components/layout/Layout';
import Container from '@/components/ui/Container';
import Button from '@/components/ui/Button';
import { useSession } from 'next-auth/react';
import { getTransactionsByEmail } from '@/services/transactionService';
import { TransactionResponse } from '@/types/transaction';

export default function TransactionsPage() {
  const router = useRouter();
  const { data: session, status } = useSession();
  const [transactions, setTransactions] = useState<TransactionResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (status === 'loading') return; // Still loading session

    if (!session?.user?.email) {
      router.push('/login');
      return;
    }

    fetchTransactions();
  }, [session, status, router]);

  const fetchTransactions = async () => {
    if (!session?.user?.email) return;

    setLoading(true);
    setError(null);

    try {
      const userTransactions = await getTransactionsByEmail(session.user.email);
      setTransactions(userTransactions);
    } catch (err) {
      console.error('Error fetching transactions:', err);
      setError('Impossible de charger les transactions. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'success':
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'failed':
      case 'error':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  if (status === 'loading') {
    return (
      <Layout>
        <div className="flex min-h-[50vh] items-center justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-gray-200 border-t-red-600"></div>
        </div>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Mes Transactions | AtlasFit</title>
        <meta
          name="description"
          content="Consultez l'historique de toutes vos transactions AtlasFit"
        />
      </Head>

      <Layout>
        <div className="bg-gray-50 py-16 sm:py-24">
          <Container>
            <div className="mx-auto max-w-6xl">
              {/* Header */}
              <div className="mb-8">
                <div className="flex items-center justify-between">
                  <div>
                    <h1 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                      Mes Transactions
                    </h1>
                    <p className="mt-2 text-lg text-gray-500">
                      Historique complet de vos paiements AtlasFit
                    </p>
                  </div>
                  <Button
                    onClick={() => router.back()}
                    variant="outline"
                    className="border-red-600 text-red-600 hover:bg-red-50 focus:ring-red-500 cursor-pointer"
                  >
                    <svg
                      className="mr-2 h-4 w-4"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M10 19l-7-7m0 0l7-7m-7 7h18"
                      />
                    </svg>
                    Retour
                  </Button>
                </div>
              </div>

              {/* Loading State */}
              {loading && (
                <div className="flex items-center justify-center py-12">
                  <div className="h-8 w-8 animate-spin rounded-full border-4 border-gray-200 border-t-red-600"></div>
                  <span className="ml-3 text-gray-600">Chargement des transactions...</span>
                </div>
              )}

              {/* Error State */}
              {error && (
                <div className="rounded-md bg-red-50 p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg
                        className="h-5 w-5 text-red-400"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800">Erreur</h3>
                      <div className="mt-2 text-sm text-red-700">{error}</div>
                      <div className="mt-4">
                        <Button
                          onClick={fetchTransactions}
                          variant="outline"
                          className="border-red-600 text-red-600 hover:bg-red-50"
                        >
                          Réessayer
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Empty State */}
              {!loading && !error && transactions.length === 0 && (
                <div className="text-center py-12">
                  <svg
                    className="mx-auto h-12 w-12 text-gray-400"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"
                    />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">Aucune transaction</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Vous n'avez encore effectué aucune transaction.
                  </p>
                  <div className="mt-6">
                    <Button
                      href="/#memberships"
                      variant="primary"
                      className="bg-red-600 hover:bg-red-700 focus:ring-red-500"
                    >
                      Voir les abonnements
                    </Button>
                  </div>
                </div>
              )}

              {/* Transactions List */}
              {!loading && !error && transactions.length > 0 && (
                <div className="space-y-6">
                  {transactions.map((transaction) => (
                    <div
                      key={transaction.id}
                      className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm"
                    >
                      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Référence</dt>
                          <dd className="mt-1 text-sm font-mono text-gray-900">{transaction.reference}</dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Montant</dt>
                          <dd className="mt-1 text-lg font-semibold text-gray-900">
                            {transaction.amount} {transaction.currency}
                          </dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Statut</dt>
                          <dd className="mt-1">
                            <span
                              className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${getStatusColor(
                                transaction.status
                              )}`}
                            >
                              {transaction.status}
                            </span>
                          </dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Date</dt>
                          <dd className="mt-1 text-sm text-gray-900">
                            {formatDate(transaction.processedAt)}
                          </dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Processeur</dt>
                          <dd className="mt-1 text-sm text-gray-900">{transaction.paymentProcessor}</dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Carte</dt>
                          <dd className="mt-1 text-sm text-gray-900">
                            **** **** **** {transaction.cardLastFour}
                          </dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-gray-500">ID Transaction</dt>
                          <dd className="mt-1 text-sm font-mono text-gray-900">{transaction.transactionReference}</dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-gray-500">ID Externe</dt>
                          <dd className="mt-1 text-sm font-mono text-gray-900">{transaction.externalTransactionId}</dd>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </Container>
        </div>
      </Layout>
    </>
  );
}
