
import { useRouter } from 'next/router';
import Head from 'next/head';
import Layout from '@/components/layout/Layout';
import Container from '@/components/ui/Container';
import Button from '@/components/ui/Button';
import { useSession } from 'next-auth/react';

export default function PaymentSuccess() {
  const router = useRouter();
  const { data: session } = useSession();
  const { reference, amount, plan } = router.query;

  // Handle download invoice action
  const handleDownloadInvoice = () => {
    console.log('Downloading invoice for reference:', reference);
    alert('Fonctionnalité de téléchargement de facture sera bientôt disponible');
  };

  // Handle view all transactions action
  const handleViewTransactions = () => {
    if (!session?.user?.email) {
      alert('Vous devez être connecté pour voir vos transactions');
      return;
    }

    // Navigate to transactions page
    router.push('/transactions');
  };

  return (
    <>
      <Head>
        <title>Paiement Réussi | AtlasFit</title>
        <meta
          name="description"
          content="Votre paiement a été traité avec succès. Bienvenue chez AtlasFit!"
        />
      </Head>

      <Layout>
        <div className="bg-gray-50 py-16 sm:py-24">
          <Container>
            <div className="mx-auto max-w-3xl text-center">
              <div className="mb-8 flex justify-center">
                <div className="rounded-full bg-green-100 p-4">
                  <svg
                    className="h-12 w-12 text-green-600"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
              </div>

              <h1 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Paiement Réussi!
              </h1>

              <p className="mt-4 text-lg text-gray-500">
                Merci pour votre abonnement à AtlasFit. Votre paiement a été traité avec succès.
              </p>

              {reference && (
                <div className="mt-6 rounded-lg bg-white p-6 shadow-sm">
                  <h2 className="text-lg font-medium text-gray-900">Détails de la transaction</h2>
                  <dl className="mt-4 grid grid-cols-1 gap-x-6 gap-y-4 sm:grid-cols-2">
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Référence</dt>
                      <dd className="mt-1 text-sm text-gray-900">{reference}</dd>
                    </div>
                    {amount && (
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Montant</dt>
                        <dd className="mt-1 text-sm text-gray-900">{amount} MAD</dd>
                      </div>
                    )}
                    {plan && (
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Formule</dt>
                        <dd className="mt-1 text-sm text-gray-900">{plan}</dd>
                      </div>
                    )}
                    {session?.user?.email && (
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Email</dt>
                        <dd className="mt-1 text-sm text-gray-900">{session.user.email}</dd>
                      </div>
                    )}
                  </dl>
                </div>
              )}

              {/* Action Buttons */}
              <div className="mt-8 space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
                <Button
                  onClick={handleDownloadInvoice}
                  variant="primary"
                  className="w-full sm:w-auto bg-red-600 hover:bg-red-700 focus:ring-red-500"
                >
                  <svg
                    className="mr-2 h-4 w-4"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                  Télécharger la facture
                </Button>

                <Button
                  onClick={handleViewTransactions}
                  variant="outline"
                  className="w-full sm:w-auto border-red-600 text-red-600 hover:bg-red-50 focus:ring-red-500"
                >
                  <svg
                    className="mr-2 h-4 w-4"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"
                    />
                  </svg>
                  Voir toutes les transactions
                </Button>
              </div>

              {/* <div className="mt-8">
                <p className="text-sm text-gray-500">
                  Vous serez redirigé vers la page d'accueil dans {countdown} seconde{countdown !== 1 ? 's' : ''}.
                </p>
                <div className="mt-6 flex justify-center space-x-4">
                  <Button href="/" variant="primary">
                    Retour à l'accueil
                  </Button>
                  <Button href="/#memberships" variant="outline">
                    Voir les abonnements
                  </Button>
                </div>
              </div> */}
            </div>
          </Container>
        </div>
      </Layout>
    </>
  );
}
