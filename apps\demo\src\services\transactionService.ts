import axios from 'axios';
import { TransactionResponse } from '@/types/subscription';

// Create an axios instance for transaction API
const transactionApi = axios.create({
  baseURL: 'http://localhost:1005',
  headers: {
    'Content-Type': 'application/json',
  },
});

export const getTransactionsByEmail = async (email: string): Promise<TransactionResponse[]> => {
  try {
    const response = await transactionApi.get<TransactionResponse[]>(`/payment/transactions/by-email/${email}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching transactions:', error);
    throw error;
  }
};
