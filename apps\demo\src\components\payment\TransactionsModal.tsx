import { Fragment } from 'react';
import { TransactionResponse } from '@/types/subscription';

interface TransactionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  transactions: TransactionResponse[];
  loading: boolean;
  error: string | null;
}

export default function TransactionsModal({
  isOpen,
  onClose,
  transactions,
  loading,
  error,
}: TransactionsModalProps) {
  if (!isOpen) return null;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'success':
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'failed':
      case 'error':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        {/* Backdrop */}
        <div
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={onClose}
        />

        {/* Modal */}
        <div className="relative w-full max-w-4xl rounded-lg bg-white shadow-xl">
          {/* Header */}
          <div className="flex items-center justify-between border-b border-gray-200 px-6 py-4">
            <h2 className="text-xl font-semibold text-gray-900">
              Toutes les transactions
            </h2>
            <button
              onClick={onClose}
              className="rounded-md p-2 text-gray-400 hover:text-gray-600"
            >
              <svg
                className="h-6 w-6"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          {/* Content */}
          <div className="max-h-96 overflow-y-auto px-6 py-4">
            {loading && (
              <div className="flex items-center justify-center py-8">
                <div className="h-8 w-8 animate-spin rounded-full border-4 border-gray-200 border-t-red-600"></div>
                <span className="ml-2 text-gray-600">Chargement des transactions...</span>
              </div>
            )}

            {error && (
              <div className="rounded-md bg-red-50 p-4">
                <div className="text-sm text-red-700">
                  Erreur lors du chargement des transactions: {error}
                </div>
              </div>
            )}

            {!loading && !error && transactions.length === 0 && (
              <div className="py-8 text-center">
                <p className="text-gray-500">Aucune transaction trouvée.</p>
              </div>
            )}

            {!loading && !error && transactions.length > 0 && (
              <div className="space-y-4">
                {transactions.map((transaction) => (
                  <div
                    key={transaction.id}
                    className="rounded-lg border border-gray-200 p-4"
                  >
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Référence</dt>
                        <dd className="mt-1 text-sm text-gray-900">{transaction.reference}</dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Montant</dt>
                        <dd className="mt-1 text-sm text-gray-900">
                          {transaction.amount} {transaction.currency}
                        </dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Statut</dt>
                        <dd className="mt-1">
                          <span
                            className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${getStatusColor(
                              transaction.status
                            )}`}
                          >
                            {transaction.status}
                          </span>
                        </dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Processeur</dt>
                        <dd className="mt-1 text-sm text-gray-900">{transaction.paymentProcessor}</dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Carte</dt>
                        <dd className="mt-1 text-sm text-gray-900">
                          **** **** **** {transaction.cardLastFour}
                        </dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Date</dt>
                        <dd className="mt-1 text-sm text-gray-900">
                          {formatDate(transaction.processedAt)}
                        </dd>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 px-6 py-4">
            <div className="flex justify-end">
              <button
                onClick={onClose}
                className="rounded-md bg-gray-600 px-4 py-2 text-sm font-medium text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
              >
                Fermer
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
