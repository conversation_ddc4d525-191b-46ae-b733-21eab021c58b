import React from 'react';
import { TransactionResponse } from '@/types/transaction';
import Button from '@/components/ui/Button';

interface TransactionsListProps {
  transactions: TransactionResponse[];
  isLoading: boolean;
  error: string | null;
  onDownloadInvoice?: (transactionId: number) => void;
}

export default function TransactionsList({ 
  transactions, 
  isLoading, 
  error, 
  onDownloadInvoice 
}: TransactionsListProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status.toUpperCase()) {
      case 'SUCCESS':
        return 'bg-green-100 text-green-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'FAILED':
        return 'bg-red-100 text-red-800';
      case 'CANCELLED':
        return 'bg-gray-100 text-gray-800';
      case 'REFUNDED':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status.toUpperCase()) {
      case 'SUCCESS':
        return 'Réussi';
      case 'PENDING':
        return 'En attente';
      case 'FAILED':
        return 'Échoué';
      case 'CANCELLED':
        return 'Annulé';
      case 'REFUNDED':
        return 'Remboursé';
      default:
        return status;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-gray-200 border-t-red-600"></div>
        <span className="ml-2 text-gray-600">Chargement des transactions...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="flex">
          <svg
            className="h-5 w-5 text-red-400"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
              clipRule="evenodd"
            />
          </svg>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Erreur</h3>
            <p className="mt-1 text-sm text-red-700">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (transactions.length === 0) {
    return (
      <div className="text-center py-8">
        <svg
          className="mx-auto h-12 w-12 text-gray-400"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"
          />
        </svg>
        <h3 className="mt-2 text-sm font-medium text-gray-900">Aucune transaction</h3>
        <p className="mt-1 text-sm text-gray-500">
          Vous n'avez encore effectué aucune transaction.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {transactions.map((transaction) => (
        <div
          key={transaction.id}
          className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm"
        >
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium text-gray-900">
                  Référence: {transaction.reference}
                </h4>
                <span
                  className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${getStatusColor(
                    transaction.status
                  )}`}
                >
                  {getStatusText(transaction.status)}
                </span>
              </div>
              
              <div className="mt-2 grid grid-cols-1 gap-2 sm:grid-cols-2">
                <div>
                  <span className="text-sm text-gray-500">Montant:</span>
                  <span className="ml-1 text-sm font-medium text-gray-900">
                    {transaction.amount} {transaction.currency || 'MAD'}
                  </span>
                </div>
                <div>
                  <span className="text-sm text-gray-500">Date:</span>
                  <span className="ml-1 text-sm text-gray-900">
                    {formatDate(transaction.createdAt)}
                  </span>
                </div>
                {transaction.subscriptionPlan && (
                  <div>
                    <span className="text-sm text-gray-500">Formule:</span>
                    <span className="ml-1 text-sm text-gray-900">
                      {transaction.subscriptionPlan}
                    </span>
                  </div>
                )}
                {transaction.paymentMethod && (
                  <div>
                    <span className="text-sm text-gray-500">Méthode:</span>
                    <span className="ml-1 text-sm text-gray-900">
                      {transaction.paymentMethod}
                    </span>
                  </div>
                )}
              </div>
              
              {transaction.description && (
                <div className="mt-2">
                  <span className="text-sm text-gray-500">Description:</span>
                  <span className="ml-1 text-sm text-gray-900">
                    {transaction.description}
                  </span>
                </div>
              )}
            </div>
            
            {transaction.status.toUpperCase() === 'SUCCESS' && onDownloadInvoice && (
              <div className="ml-4">
                <Button
                  onClick={() => onDownloadInvoice(transaction.id)}
                  variant="outline"
                  size="sm"
                  className="border-red-600 text-red-600 hover:bg-red-50"
                >
                  <svg
                    className="mr-1 h-4 w-4"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                  Facture
                </Button>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
}
